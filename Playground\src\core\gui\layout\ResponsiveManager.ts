import { Players, UserInputService } from "@rbxts/services";

export interface ScreenSize {
    width: number;
    height: number;
    aspectRatio: number;
}

export interface ResponsiveBreakpoints {
    mobile: number;
    tablet: number;
    desktop: number;
}

export type DeviceType = "mobile" | "tablet" | "desktop";

export class ResponsiveManager {
    private static instance: ResponsiveManager;
    private currentScreenSize: ScreenSize;
    private callbacks: Array<(screenSize: ScreenSize) => void> = [];
    
    // Standard breakpoints based on common device sizes
    private breakpoints: ResponsiveBreakpoints = {
        mobile: 768,   // Width below this is mobile
        tablet: 1024,  // Width below this (but above mobile) is tablet
        desktop: 1024  // Width above this is desktop
    };

    private constructor() {
        this.currentScreenSize = this.calculateScreenSize();
        this.setupViewportListener();
    }

    public static getInstance(): ResponsiveManager {
        if (!ResponsiveManager.instance) {
            ResponsiveManager.instance = new ResponsiveManager();
        }
        return ResponsiveManager.instance;
    }

    private calculateScreenSize(): ScreenSize {
        const player = Players.LocalPlayer;
        const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;
        
        // Get the actual viewport size
        const viewportSize = playerGui.GetGuiInset()[1];
        const width = viewportSize.X;
        const height = viewportSize.Y;
        
        return {
            width,
            height,
            aspectRatio: width / height
        };
    }

    private setupViewportListener(): void {
        const player = Players.LocalPlayer;
        const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;
        
        // Listen for viewport size changes
        playerGui.GetPropertyChangedSignal("AbsoluteSize").Connect(() => {
            const newScreenSize = this.calculateScreenSize();
            
            // Only update if size actually changed
            if (newScreenSize.width !== this.currentScreenSize.width || 
                newScreenSize.height !== this.currentScreenSize.height) {
                this.currentScreenSize = newScreenSize;
                this.notifyCallbacks();
            }
        });
    }

    private notifyCallbacks(): void {
        this.callbacks.forEach(callback => {
            try {
                callback(this.currentScreenSize);
            } catch (error) {
                warn(`ResponsiveManager callback error: ${error}`);
            }
        });
    }

    public getScreenSize(): ScreenSize {
        return { ...this.currentScreenSize };
    }

    public getDeviceType(): DeviceType {
        const width = this.currentScreenSize.width;
        
        if (width < this.breakpoints.mobile) {
            return "mobile";
        } else if (width < this.breakpoints.tablet) {
            return "tablet";
        } else {
            return "desktop";
        }
    }

    public isMobile(): boolean {
        return this.getDeviceType() === "mobile" || UserInputService.TouchEnabled;
    }

    public isTablet(): boolean {
        return this.getDeviceType() === "tablet";
    }

    public isDesktop(): boolean {
        return this.getDeviceType() === "desktop";
    }

    public onScreenSizeChange(callback: (screenSize: ScreenSize) => void): () => void {
        this.callbacks.push(callback);
        
        // Return unsubscribe function
        return () => {
            const index = this.callbacks.indexOf(callback);
            if (index > -1) {
                this.callbacks.splice(index, 1);
            }
        };
    }

    // Utility functions for responsive positioning
    public getRelativePosition(
        desiredPosition: { x: number; y: number },
        anchor: { x: number; y: number } = { x: 0, y: 0 }
    ): UDim2 {
        const screenSize = this.getScreenSize();
        
        // Convert pixel positions to scale values
        const scaleX = desiredPosition.x / screenSize.width;
        const scaleY = desiredPosition.y / screenSize.height;
        
        return new UDim2(scaleX, 0, scaleY, 0);
    }

    public getResponsiveSize(
        desiredSize: { width: number; height: number },
        minSize?: { width: number; height: number },
        maxSize?: { width: number; height: number }
    ): UDim2 {
        const screenSize = this.getScreenSize();
        
        // Convert pixel sizes to scale values
        let scaleWidth = desiredSize.width / screenSize.width;
        let scaleHeight = desiredSize.height / screenSize.height;
        
        // Apply constraints if provided
        if (minSize) {
            const minScaleWidth = minSize.width / screenSize.width;
            const minScaleHeight = minSize.height / screenSize.height;
            scaleWidth = math.max(scaleWidth, minScaleWidth);
            scaleHeight = math.max(scaleHeight, minScaleHeight);
        }
        
        if (maxSize) {
            const maxScaleWidth = maxSize.width / screenSize.width;
            const maxScaleHeight = maxSize.height / screenSize.height;
            scaleWidth = math.min(scaleWidth, maxScaleWidth);
            scaleHeight = math.min(scaleHeight, maxScaleHeight);
        }
        
        return new UDim2(scaleWidth, 0, scaleHeight, 0);
    }

    public getResponsiveMargin(pixelMargin: number): number {
        const screenSize = this.getScreenSize();
        const deviceType = this.getDeviceType();
        
        // Adjust margin based on device type
        let multiplier = 1;
        switch (deviceType) {
            case "mobile":
                multiplier = 0.8; // Smaller margins on mobile
                break;
            case "tablet":
                multiplier = 1.0;
                break;
            case "desktop":
                multiplier = 1.2; // Larger margins on desktop
                break;
        }
        
        return pixelMargin * multiplier;
    }

    public getSafeAreaInsets(): { top: number; bottom: number; left: number; right: number } {
        const deviceType = this.getDeviceType();
        
        if (deviceType === "mobile") {
            // Mobile devices have reserved areas for controls
            return {
                top: 0,
                bottom: 80,    // Bottom area for jump button and thumbstick
                left: 80,      // Left area for thumbstick
                right: 80      // Right area for jump button
            };
        }
        
        return { top: 0, bottom: 0, left: 0, right: 0 };
    }
}
