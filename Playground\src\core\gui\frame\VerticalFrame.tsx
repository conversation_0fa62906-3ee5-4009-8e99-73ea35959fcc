import * as React from "@rbxts/react";
import { COLORS, SIZES } from "../../design";
import { VerticalFrameProps } from "./types";

export function VerticalFrame(props: VerticalFrameProps): React.ReactElement {
  const backgroundColor = props.backgroundColor ?? COLORS.bg.base;
  const backgroundTransparency = props.backgroundTransparency ?? 1;
  const padding = props.padding ?? SIZES.padding;
  const spacing = props.spacing ?? SIZES.margin;

  // Smart sizing: if fitContent is true or no size specified, use AutomaticSize
  const fitContent = props.fitContent ?? (props.size === undefined);
  const size = props.size ?? (fitContent ? new UDim2(1, 0, 0, 0) : new UDim2(1, 0, 1, 0));
  const autoSize = props.autoSize ?? (fitContent ? Enum.AutomaticSize.Y : Enum.AutomaticSize.None);

  return (
    <frame
      BackgroundColor3={Color3.fromHex(backgroundColor)}
      BackgroundTransparency={backgroundTransparency}
      Size={size}
      Position={props.position}
      AnchorPoint={props.anchorPoint}
      LayoutOrder={props.layoutOrder}
      BorderSizePixel={0}
      ZIndex={props.zIndex}
      AutomaticSize={autoSize}
      ClipsDescendants={true} // Prevent content from overflowing the frame
    >
      <uilistlayout
        Padding={new UDim(0, spacing)}
        SortOrder={Enum.SortOrder.LayoutOrder}
        FillDirection={Enum.FillDirection.Vertical}
        HorizontalAlignment={props.horizontalAlignment ?? Enum.HorizontalAlignment.Left}
        VerticalAlignment={Enum.VerticalAlignment.Top}
      />

      <uipadding
        PaddingTop={new UDim(0, padding)}
        PaddingBottom={new UDim(0, padding)}
        PaddingLeft={new UDim(0, padding)}
        PaddingRight={new UDim(0, padding)}
      />

      {props.children}
    </frame>
  );
}